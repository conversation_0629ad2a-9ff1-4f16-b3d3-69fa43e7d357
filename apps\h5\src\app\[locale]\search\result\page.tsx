'use client'
import { useC<PERSON>back, useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import searchEmpty from '@public/images/search_empty.png'
import {
  generateOSSUrl,
  GqlError,
  PRECISE_RATE_LIMIT,
  resolveCatchMessage,
  storeConfigSelector,
  TCatchMessage,
  useCurrentTime,
  useLazySearchProductsQuery,
  usePagination,
  useRateLimitHandler,
  useToastContext,
} from '@ninebot/core'
import { HomeProduct } from '@ninebot/core'
import { useVolcAnalytics } from '@ninebot/core/src/businessHooks'
import { TRACK_EVENT } from '@ninebot/core/src/constants'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd-mobile'

import {
  CustomEmpty,
  CustomSearchBar,
  ProductCard,
  ProductCardSkeleton,
  RecommendedProducts,
  Skeleton,
} from '@/components'
import { useRouter } from '@/i18n/navigation'
import { updateProductList } from '@/utils/format'

export default function SearchResultPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const getI18nString = useTranslations('Common')
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()
  const { page, pageSize, handlePageChange, handlePageNext } = usePagination(1, 24)
  const [searchProducts] = useLazySearchProductsQuery()
  const { reportEvent } = useVolcAnalytics()

  const storeConfig = useAppSelector(storeConfigSelector)
  const defaultSearchWord = storeConfig?.default_search_words || ''

  const toast = useToastContext()

  // 搜索限流处理
  const searchRateLimit = useRateLimitHandler({
    mode: 'toast',
  })

  const [searchQuery, setSearchQuery] = useState('')
  const [localSearchQuery, setLocalSearchQuery] = useState('')
  const keyword = searchParams.get('q') || ''
  const [productsList, setProductsList] = useState<HomeProduct[]>([])
  const [hasMore, setHasMore] = useState(false)
  const [total, setTotal] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [hasInit, setHasInit] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [canShow, setCanShow] = useState(true)

  const handleSearch = useCallback(
    async (query: string, isheader = false) => {
      if (isheader) {
        handlePageChange(1)
      }
      setIsLoading(page === 1 || isheader)
      setLocalSearchQuery(query)
      await searchProducts({
        search: query,
        pageSize,
        currentPage: isheader ? 1 : page,
        customAttributesV3Filter: {
          filter_attributes: [
            'product_tag',
            'paymeng_method',
            'max_usage_limit_ncoins',
            'is_insurance',
            'insurance_link',
          ],
        },
      })
        .unwrap()
        .then((res) => {
          if (res?.products) {
            const {
              items: newProducts,
              page_info: pageInfo,
              total_count: totalCount,
            } = res.products

            setTotal(totalCount || 0)
            if (page === 1 || isheader) {
              setProductsList(newProducts as HomeProduct[])
              reportEvent(TRACK_EVENT.shop_search_result_exposure, {
                search_word: query,
              })
            } else {
              setProductsList((prevProducts) =>
                updateProductList(prevProducts, newProducts as HomeProduct[]),
              )
            }

            // if (searchRef.current && !isLoadingMore) {
            //   searchRef.current?.scrollToOffset?.({ offset: 0, animated: false });
            // }

            const timer = setTimeout(() => {
              if (!!pageInfo?.total_pages && !!pageInfo?.current_page) {
                setHasMore(pageInfo.total_pages > pageInfo.current_page)
              }
              setIsLoading(false)
              setIsLoadingMore(false)
              setHasInit(true)
            }, 15)

            if (pageInfo?.total_pages === pageInfo?.current_page) {
              handlePageChange(1)
            }

            return () => clearTimeout(timer)
          }
        })
        .catch((error) => {
          setIsLoading(false)
          setIsLoadingMore(false)

          const err = error as GqlError
          // 精准限流
          if (err?.type === PRECISE_RATE_LIMIT) {
            searchRateLimit.handleError(err.retryMs!)
          }

          // 如果不是限流错误，显示普通错误提示
          toast.show({
            icon: 'fail',
            content: String(resolveCatchMessage(error as TCatchMessage)),
          })
        })
    },
    [handlePageChange, page, pageSize, searchProducts, toast, reportEvent, searchRateLimit],
  )

  const handleLoadMore = useCallback(() => {
    setIsLoadingMore(true)
    handlePageNext()
  }, [handlePageNext])

  useEffect(() => {
    if (!hasInit) {
      if (searchQuery?.trim()) {
        handleSearch(searchQuery.trim())
      }
    }
  }, [handleSearch, searchQuery, hasInit])

  useEffect(() => {
    if (!hasInit && !!keyword) {
      setSearchQuery(keyword)
      setLocalSearchQuery(keyword)
    }
  }, [keyword, hasInit])

  useEffect(() => {
    if (page > 1) {
      handleSearch(searchQuery)
    }
  }, [page, handleSearch, searchQuery])

  useEffect(() => {
    fetchCurrentTime()
  }, [fetchCurrentTime])

  return (
    <div className="min-h-screen bg-white px-base-16">
      <CustomSearchBar
        searchValue={searchQuery}
        setSearchValue={setSearchQuery}
        onSearch={handleSearch}
        onCancel={() => router.back()}
        isResultPage={true}
        setCanShow={setCanShow}
        placeholder={defaultSearchWord}
      />

      {canShow ? (
        isLoading ? (
          <div className="flex flex-1 flex-col">
            <Skeleton
              style={{
                height: 20,
                width: 206,
                borderRadius: 4,
                marginBottom: 20,
              }}
            />
            <ProductCardSkeleton length={4} />
          </div>
        ) : (
          <>
            {productsList.length > 0 ? (
              <>
                <div className="mb-8 mt-base-16 text-[14px] leading-none text-[#222223]">
                  {getI18nString('related_results', { key: localSearchQuery, key2: total })
                    .split(new RegExp(`(${localSearchQuery})`, 'gi'))
                    .map((part, index) => (
                      <span
                        key={index}
                        className={
                          part.toLowerCase() === localSearchQuery.toLowerCase() ? 'text-[18px]' : ''
                        }>
                        {part}
                      </span>
                    ))}
                </div>

                {/* 商品列表 */}
                <div className="grid grid-cols-2 gap-x-6">
                  {productsList.map((item) => (
                    <div key={item.sku}>
                      <ProductCard
                        product={item}
                        currentTime={currentTime}
                        pageType="searchResult"
                      />
                    </div>
                  ))}
                </div>
                {isLoadingMore ? (
                  <div className="flex flex-1 flex-col">
                    <ProductCardSkeleton />
                  </div>
                ) : (
                  hasMore && (
                    <div className="my-base-24 flex items-center justify-center">
                      <Button className="nb-button !h-16" onClick={handleLoadMore}>
                        {getI18nString('more')}
                      </Button>
                    </div>
                  )
                )}
              </>
            ) : (
              !isLoadingMore && (
                <CustomEmpty
                  source={generateOSSUrl('/images/search_empty.png') || searchEmpty.src}
                  style="h-[133px] flex items-center justify-center mt-[32px] mb-[64px]"
                  description={getI18nString('no_results', { key: keyword })}
                  descriptionStyle="font-miSansMedium380 text-[14px] text-[#86868B] leading-[17px] text-center"
                />
              )
            )}

            <RecommendedProducts />
          </>
        )
      ) : null}
    </div>
  )
}
