# 高德地图安全密钥迁移

## 概述

为了增强安全性，我们将高德地图的安全密钥从客户端迁移到服务器端。这样可以避免将密钥直接暴露给Web端代码，减少密钥被滥用的风险。

## 变更内容

### 环境变量变更

| 原变量名 | 新变量名 | 说明 |
|---------|---------|------|
| `NEXT_PUBLIC_AMAP_SECURITY_CODE` | `AMAP_SECURITY_CODE` | 高德地图安全密钥，现在仅在服务器端使用 |

### 架构变更

```
之前的架构：
客户端 → 直接调用高德地图API (密钥暴露)

现在的架构：
客户端 → Next.js API代理 → 高德地图API (密钥安全)
```

## 参考文档

[text](https://lbs.amap.com/api/javascript-api-v2/guide/abc/jscode)
