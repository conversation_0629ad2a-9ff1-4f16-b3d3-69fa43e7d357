'use client'
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useState } from 'react'

import { QUID_KEY } from '../constants'
import { quidSelector } from '../store'
import { useAppSelector } from '../store/hooks'
import { getShopApiUrl } from '../utils/envUtil'

/**
 * useCurrentTime
 * - 通过请求 URL 获取服务器当前时间戳
 * - 返回时间戳、错误信息
 */
const useCurrentTime = () => {
  const [timestamp, setTimestamp] = useState<string>('')
  const [error, setError] = useState<string>('')
  const quid = useAppSelector(quidSelector)

  const fetchTimestamp = useCallback(async () => {
    setError('')

    try {
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      }

      // 添加 QUID header
      if (quid) {
        headers[QUID_KEY.toUpperCase()] = quid
      }

      const response = await fetch(`${getShopApiUrl()}/current.php`, {
        headers,
      })

      const data = await response.json()
      if (data?.current_time) {
        setTimestamp(data.current_time)

        return data.current_time
      }
    } catch (err: any) {
      setError(err?.message)
    }
  }, [quid])

  return { timestamp, error, fetchCurrentTime: fetchTimestamp }
}

export default useCurrentTime
