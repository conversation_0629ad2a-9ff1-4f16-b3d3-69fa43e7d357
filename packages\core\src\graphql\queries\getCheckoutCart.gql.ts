import { gql } from '../../utils'
import {
  PRODUCT_EXTENSION_INFO_FRAGMENT,
  PRODUCT_FRAGMENT,
  PRODUCT_PRICE_RANGE_FRAGMENT,
} from '../fragments'

/**
 * 获取购物车信息
 * filters: { filter_attributes: [max_usage_limit_ncoins] }
 */
export const GET_CART_QTY = gql`
  query getCartQty(
    $currentPage: Int
    $pageSize: Int
    $customAttributesV3Filter: AttributeFilterInput
  ) {
    customer {
      shipping_cart(currentPage: $currentPage, pageSize: $pageSize) {
        items_count
        items {
          uid
          quantity
          is_selected
          parent_sku
          extension_info {
            ...productExtensionInfoFragment
          }
          product {
            ...productFragment
            ...productPriceRangeFragment
            delivery_method
            salable_qty
            paymeng_method
            custom_attributesV3(filters: $customAttributesV3Filter) {
              items {
                code
                ... on AttributeValue {
                  value
                }
              }
            }
            url_path
            url_suffix
            url_key
          }
        }
      }
    }
    customAttributeMetadata(attributes: [{ attribute_code: "delivery_method", entity_type: "4" }]) {
      items {
        attribute_code
        attribute_options {
          label
          value
        }
      }
    }
  }
  ${PRODUCT_FRAGMENT}
  ${PRODUCT_EXTENSION_INFO_FRAGMENT}
  ${PRODUCT_PRICE_RANGE_FRAGMENT}
`
