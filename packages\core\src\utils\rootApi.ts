import {
  BaseQueryFn,
  create<PERSON><PERSON>,
  FetchBaseQueryArgs,
  FetchBaseQueryError,
  FetchBaseQueryMeta,
  retry,
} from '@reduxjs/toolkit/query/react'
import { Mutex } from 'async-mutex'
import { isUndefined } from 'lodash-es'

import { KEY_AUTHORIZATION, KEY_X_AUTHORIZATION, QUID_KEY } from '../constants'
import { loggedOut, refreshUserToken, setUserAuthToken, updateRateLimitEnabled } from '../store'
import { RootState } from '../store/store'

import { TGraphqlRequestResponse } from './graphql/gqlRequest'
import { getShopApiUrl } from './envUtil'
import { GqlError, UN_AUTHORIZED, validateHttpStatus } from './graphql'
import { getEncodeShopUrlBasicAuth, gqlRequest } from './helper'
import { isShopUrlBasicAuth } from './util'

type TGqlBaseQueryOptions = {
  baseUrl: string
  prepareHeaders?: FetchBaseQueryArgs['prepareHeaders']
}

type TGqlBaseQueryArgs = {
  document: string
  variables?: Record<string, unknown> | string | number
  timeout?: number
  method?: 'GET' | 'POST'
}

export type TGqlBaseQueryError = {
  status: number
  data?: string
}

type TCustomExtraOptions = {
  // 是否需要在 header 添加授权信息，默认值 false
  isAuth?: boolean
  // credentials 参数
  credentials?: RequestInit['credentials']
}

// 最大重试请求数
const MAX_RETRIES = 1

// Mutex to handle concurrent requests
const mutex = new Mutex()

const gqlBaseQuery = (
  args: TGqlBaseQueryOptions,
): BaseQueryFn<
  TGqlBaseQueryArgs,
  unknown,
  FetchBaseQueryError,
  TCustomExtraOptions,
  FetchBaseQueryMeta
> => {
  return async (arg, api, extraOptions = {}) => {
    const { document, variables, method, timeout = 1000 * 60 } = arg
    const { baseUrl, prepareHeaders } = args
    const { isAuth = false, credentials = 'omit' } = extraOptions

    // request response
    let response: TGraphqlRequestResponse<unknown>
    // timeoutId
    let timeoutId: ReturnType<typeof setTimeout> | undefined
    // prepared method
    let preparedMethod: TGqlBaseQueryArgs['method']

    // 处理 method，如果没传 method，则根据 type 自动设置 method 值
    if (api.type === 'query') {
      preparedMethod = isUndefined(method) ? 'GET' : method
    }

    if (api.type === 'mutation') {
      preparedMethod = isUndefined(method) ? 'POST' : method
    }

    // 增加自定义 Header 信息
    const prepareHeadersFun = prepareHeaders ?? ((x) => x)
    const preparedHeaders = await prepareHeadersFun(new Headers(), {
      getState: api.getState,
      arg: { url: baseUrl, params: { ...arg, method: preparedMethod } },
      extra: api.extra,
      endpoint: api.endpoint,
      type: api.type,
      method: preparedMethod,
      forced: api.forced,
      extraOptions,
    } as Parameters<typeof prepareHeadersFun>[1] & { method: TGqlBaseQueryArgs['method'] })

    // 添加x-platform头部信息 (由graphql/gqlRequest.ts处理)

    // 根据 isAuth 判断发送请求是否携带 token
    if (preparedHeaders && !isAuth) {
      preparedHeaders.delete(isShopUrlBasicAuth ? KEY_X_AUTHORIZATION : KEY_AUTHORIZATION)
    }

    // wait until the mutex is available without locking it
    await mutex.waitForUnlock()

    try {
      // 仅当 mutex 未锁定时，开始记录超时
      if (!mutex.isLocked()) {
        timeoutId = setTimeout(() => {
          api.abort('获取数据异常，请重试！')
        }, timeout)
      }

      response = await gqlRequest.request(document, variables, {
        url: baseUrl,
        signal: api.signal,
        type: api.type,
        method: preparedMethod,
        headers: preparedHeaders || new Headers(),
        // 控制请求是否携带 cookie 信息
        credentials,
      })

      // 处理 429 状态码，跳转到限流页面
      if (response.status === 429) {
        const state = api.getState() as RootState
        const {
          global: { rateLimitEnabled },
        } = state
        if (!rateLimitEnabled) {
          api.dispatch(updateRateLimitEnabled(true))
          window.location.href = '/limit'
        }

        const { error, status, data, errorType } = response
        throw new GqlError(error!, status, data, errorType)
      }

      // 427/428 精准限流
      if ([427, 428].includes(response.status)) {
        const { error, status, data, errorType, retryMs } = response
        throw new GqlError(error!, status, data, errorType, retryMs)
      }

      // token 失效后，锁定后续请求，重新刷新 token，再发送后续请求
      if (isAuth && response.isError && response.errorType === UN_AUTHORIZED) {
        // 先检查是否锁住
        if (!mutex.isLocked()) {
          const state = api.getState() as RootState
          const {
            user: { authToken },
          } = state

          // 设置锁定状态
          const release = await mutex.acquire()
          // request a new token
          const refreshResult = await api
            .dispatch(
              refreshUserToken({
                refreshToken: authToken?.refreshToken,
              }),
            )
            .unwrap()

          if (refreshResult) {
            // 保存新的 token 信息
            api.dispatch(
              setUserAuthToken({
                refreshToken: refreshResult.refresh_token.token,
                refreshTokenExpired: refreshResult.refresh_token.expired,
                token: refreshResult.token.token,
                tokenExpired: refreshResult.token.expired,
              }),
            )

            // 替换为新 token
            preparedHeaders?.set(
              isShopUrlBasicAuth ? KEY_X_AUTHORIZATION : KEY_AUTHORIZATION,
              `Bearer ${refreshResult.token.token}`,
            )

            // retry the initial query
            response = await gqlRequest.request(document, variables, {
              url: baseUrl,
              signal: api.signal,
              type: api.type,
              method: preparedMethod,
              headers: preparedHeaders || new Headers(),
              credentials,
            })

            // release must be called once the mutex should be released again.
            release()
          } else {
            // 退出登录，清除 token 信息
            api.dispatch(loggedOut())

            // TODO: 如果刷新 token 也失败了，重新走授权流程
            // const tokenResult = await api.dispatch(fetchUserToken()).unwrap()
            // if (tokenResult) {
            //   api.dispatch(setUserAuthToken(tokenResult))
            // }
            // release must be called once the mutex should be released again.
            release()
          }
        } else {
          // wait until the mutex is available without locking it
          await mutex.waitForUnlock()

          const state = api.getState() as RootState
          const {
            user: { authToken },
          } = state

          // 替换为新 token
          preparedHeaders?.set(
            isShopUrlBasicAuth ? KEY_X_AUTHORIZATION : KEY_AUTHORIZATION,
            `Bearer ${authToken?.token}`,
          )

          response = await gqlRequest.request(document, variables, {
            url: baseUrl,
            signal: api.signal,
            type: api.type,
            method: preparedMethod,
            headers: preparedHeaders || new Headers(),
            credentials,
          })
        }
      }

      // status 不等于200的情况，或有报错时，抛出错误
      if (response.status !== 200 || response.error) {
        const { error, status, data, errorType } = response
        throw new GqlError(error!, status, data, errorType)
      }

      return { data: response.data }
    } catch (error) {
      // 处理自定义 Error 错误
      if (error instanceof GqlError) {
        const { status, message, type, retryMs } = error

        return {
          error: {
            status,
            data: message,
            type,
            retryMs,
          },
        }
      }

      return {
        error: {
          status: 500,
          data: String(error),
          type: 'default',
        },
      }
    } finally {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }
}

/**
 * retry 包裹函数
 */
const gqlQueryWithRetry = (options: { baseUrl: string }) => {
  return retry(
    gqlBaseQuery({
      baseUrl: options.baseUrl,
      prepareHeaders: async (headers, api) => {
        const state = api.getState() as RootState
        const { user, global } = state

        // 添加请求为json形式
        headers.append('Content-type', 'application/json; charset=utf-8')
        // 添加限流 QUID
        headers.append(QUID_KEY.toUpperCase(), global.quid)

        // 如果配置 Auth，Basic 添加到 header
        if (isShopUrlBasicAuth) {
          headers.append(KEY_AUTHORIZATION, getEncodeShopUrlBasicAuth())
          // 如果已登录，将 token 添加到 header
          if (user.isLoggedIn) {
            headers.append(KEY_X_AUTHORIZATION, `Bearer ${user?.authToken?.token}`)
          }
        }

        // 如果已登录，将 token 添加到 header
        if (!isShopUrlBasicAuth && user.isLoggedIn) {
          headers.append(KEY_AUTHORIZATION, `Bearer ${user?.authToken?.token}`)
        }

        return headers
      },
    }),
    {
      // maxRetries: MAX_RETRIES,
      retryCondition: (error, args, { attempt, extraOptions }) => {
        // retryCondition 函数返回 true 时，会重新发送请求

        const extraOptionsClone = extraOptions as { maxRetries?: number }
        let resolveMaxRetries = MAX_RETRIES
        if (!isUndefined(extraOptionsClone) && extraOptionsClone?.maxRetries) {
          resolveMaxRetries = extraOptionsClone.maxRetries
        }

        // 如果状态码是 427/428/429，不重试
        return (
          attempt <= resolveMaxRetries &&
          !validateHttpStatus((error as FetchBaseQueryError)?.status as number) &&
          ![427, 428, 429].includes((error as FetchBaseQueryError)?.status as number)
        )
      },
    },
  )
}

const rootApi = createApi({
  reducerPath: 'rootApi',
  baseQuery: gqlQueryWithRetry({
    baseUrl: `${getShopApiUrl()}/graphql`,
  }),
  endpoints: () => ({}),
  keepUnusedDataFor: 0, // 默认缓存60s
  tagTypes: [],
})

export default rootApi
